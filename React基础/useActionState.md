<!--
 * @File name: 
 * @Author: <EMAIL>
 * @Version: V1.0
 * @Date: 2025-07-30 23:11:26
 * @Description: 
 * Copyright (C) 2024-{year} Tsing Micro Technology Inc All rights reserved.
-->
# useActionState

`useActionState` 是 React 19 引入的一个新 Hook，主要用于简化异步操作（如表单提交、API 调用等）的状态管理。以下是它的核心作用和注意事项：

---

### **作用**

1. **统一管理异步状态**：
   - 自动处理异步操作的 `pending`（加载中）、`error`（错误）和 `data`（成功结果）状态。
   - 无需手动维护 `useState` 和 `useEffect` 的复杂逻辑。

2. **与表单深度集成**：
   - 可以直接绑定到 `<form action>`，简化表单提交逻辑。
   - 支持通过 `FormData` 获取表单数据。

3. **优化用户体验**：
   - 提供 `isPending` 状态，方便禁用按钮或显示加载状态。
   - 自动处理竞态问题（如多次快速提交）。

4. **支持渐进式增强**：
   - 可以与服务端组件（Server Components）结合使用，支持服务端验证和响应。

---

### **注意事项**

1. **依赖 React 19+**：
   - 确保项目使用的是 React 19 或更高版本。

2. **异步操作必须返回 Promise**：
   - `useActionState` 的回调函数必须返回 `Promise`，否则会抛出错误。

3. **错误处理**：
   - 如果异步操作抛出错误，`useActionState` 会自动捕获并更新 `error` 状态。
   - 建议在回调函数中显式返回错误信息（如 `return { error: 'xxx' }`），而非直接抛出异常。

4. **避免副作用**：
   - 回调函数应专注于状态更新，避免直接操作 DOM 或触发其他副作用。

5. **竞态问题**：
   - 默认会取消未完成的异步操作（如用户快速多次提交），但需确保操作是幂等的。

6. **初始状态**：
   - 通过第二个参数设置初始状态（如 `null` 或 `{ data: [] }`）。

7. **性能优化**：
   - 对于高频操作（如实时搜索），建议结合 `debounce` 或 `throttle` 使用。

---

### **适用场景**

- 表单提交（如登录、注册）。
- 数据获取（如搜索、分页）。
- 任何需要管理 `pending/error/data` 状态的异步操作。

## 案例

```jsx
import { useActionState } from 'react';

const FormComponent = () => {
  const [state, submitAction, isPending] = useActionState(
    async (previousState, formData) => {
      // 模拟异步操作
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      const name = formData.get('name');
      if (!name) {
        return { error: '请输入姓名' };
      }
      
      return { success: true, data: `提交成功: ${name}` };
    },
    null
  );

  return (
    <form action={submitAction}>
      <input type="text" name="name" placeholder="请输入姓名" />
      <button type="submit" disabled={isPending}>
        {isPending ? '提交中...' : '提交'}
      </button>
      
      {state?.error && <p style={{ color: 'red' }}>{state.error}</p>}
      {state?.success && <p style={{ color: 'green' }}>{state.data}</p>}
    </form>
  );
};

export default FormComponent;
```