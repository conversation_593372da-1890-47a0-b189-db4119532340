/*
 * @File name:
 * @Author: <EMAIL>
 * @Version: V1.0
 * @Date: 2025-05-11 01:11:32
 * @Description: 入口组件
 * Copyright (C) 2024-{year} Tsing Micro Technology Inc All rights reserved.
 */
import { useState } from "react";
import "./App.css";
import JsxBase from "./JsxBase";
import ClassComp from "./ClassComp";
import FunComp from "./FunComp";
import RefCom from "./RefCom";
import RefClass from "./RefClass";
import HocClassCom from "./HocClassCom";
import HocFunComp from "./HocFunComp";
import ForwardRefExample from "./ForwardRefExample";
// import OldContext from "./OldContext";
import NewContext from "./NewContext";
import HookContext from "./HookContext";
import ShouldComponentUpdateComp from "./ShouldComponentUpdateComp";
import PureComponentComp from "./PureComponentComp";
import ReactMemoComp from "./ReactMemoComp";
import RenderPropsComp from "./RenderPropsComp";
import CreatePortalComp from "./CreatePortalComp";
import ErrorBoundary from "./ErrorBoundary";
import UseStateComp from "./UseStateComp";
// react19 相关 hook
import UseActionStateComp from "./UseActionStateComp";
import UseFormStatusComp from "./UseFormStatusComp";

function App() {
  const [count1, setCount1] = useState(1);
  const [count2, setCount2] = useState(1);
  return (
    <div className="App">
      <header className="App-header">
        {/* 直接写dom */}
        <p>hello React!</p>
        {/* jsx基础语法 */}
        <JsxBase />
        {/* 类组件 */}

        <ClassComp name="类组件" age={24} content={<p>这是内容</p>}>
          {/* 这里放置的内容会作为children传递给ClassComp */}
          <h3>这是ClassComp子内容</h3>
          <p>这是通过children传递的内容</p>
        </ClassComp>
        {/* 函数组件 */}
        <FunComp name="函数式组件" age={24} content={<p>这是内容</p>}>
          {/* 这里放置的内容会作为children传递给ClassComp */}
          <h3>这是FunComp子内容</h3>
          <p>这是通过children传递的内容</p>
        </FunComp>
      </header>
      {/* ref && ref 转发 */}
      <RefCom />
      <RefClass />
      {/* 高阶组件 */}
      <HocClassCom />
      <HocFunComp />
      {/* forwardRef */}
      <ForwardRefExample />
      {/* 上下文 context 三种形态和用法 */}
      {/* 已经移除了作为了解 */}
      {/* <OldContext/> */}
      <NewContext />
      <HookContext />
      <ShouldComponentUpdateComp />
      <PureComponentComp />
      {/* React.memo */}
      <div>
        <div>{count1}</div>
        <button onClick={() => setCount1(count1 + 1)}>React.memo的+1</button>
        <ReactMemoComp counter={count2} setCount={setCount2} />
        <RenderPropsComp/>
        <CreatePortalComp/>
        <ErrorBoundary>
          <HocClassCom />
        </ErrorBoundary>
      </div>
      <button onClick={() => console.log('按钮事件')}>事件按钮</button>
      <UseStateComp/>
      {/* react19 */}
      <UseActionStateComp/>
      <UseFormStatusComp/>
    </div>
  );
}

export default App;

document.getElementById('root').onclick = function (e) {
  console.log(e.target,'真实dom事件冒泡');
  // e.stopPropagation();
};