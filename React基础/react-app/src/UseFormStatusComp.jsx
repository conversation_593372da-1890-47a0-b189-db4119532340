import { useState, useMemo, useRef } from 'react';
import { useFormStatus } from 'react-dom';

export default function UsernameForm() {
  const { pending, data } = useFormStatus();
  const [showSubmitted, setShowSubmitted] = useState(false);
  const submittedUsername = useRef(null);
  const timeoutId = useRef(null);

  useMemo(() => {
    if (pending) {
      submittedUsername.current = data?.get('username');
      if (timeoutId.current != null) {
        clearTimeout(timeoutId.current);
      }

      timeoutId.current = setTimeout(() => {
        timeoutId.current = null;
        setShowSubmitted(false);
      }, 2000);
      setShowSubmitted(true);
    }
  }, [pending, data]);

  return (
    <>
      <label>请求用户名：</label><br />
      <input type="text" name="username" />
      <button type="submit" disabled={pending}>
        {pending ? '提交中……' : '提交'}
      </button>
      {showSubmitted ? (
        <p>提交请求用户名：{submittedUsername.current}</p>
      ) : null}
    </>
  );
}