import { useActionState } from 'react';

const FormComponent = () => {
  const [state, submitAction, isPending] = useActionState(
    async (previousState, formData) => {
      // 模拟异步操作
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      const name = formData.get('name');
      if (!name) {
        return { error: '请输入姓名' };
      }
      
      return { success: true, data: `提交成功: ${name}` };
    },
    null
  );

  return (
    <form action={submitAction}>
      <input type="text" name="name" placeholder="请输入姓名" />
      <button type="submit" disabled={isPending}>
        {isPending ? '提交中...' : '提交'}
      </button>
      
      {state?.error && <p style={{ color: 'red' }}>{state.error}</p>}
      {state?.success && <p style={{ color: 'green' }}>{state.data}</p>}
    </form>
  );
};

export default FormComponent;